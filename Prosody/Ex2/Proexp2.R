# Enhanced R script for professional prosody visualization
# Chinese phrase: "mei-zhi mao chi-zhe yi pen mao liang"
# Academic-standard pitch contour plot following phonetics journal standards

# Load required libraries
library(readxl)
library(ggplot2)
library(dplyr)
library(scales)

# Load and prepare data
intonation <- read_excel("normf2.xlsx")
intonation$point <- as.numeric(intonation$point)

# Print data structure for verification
cat("Data structure:\n")
print(str(intonation))
cat("\nFirst few rows:\n")
print(head(intonation))
cat("\nSummary statistics:\n")
print(summary(intonation))

# Define syllable labels and focal region
syllable_labels <- c("mei", "zhi", "mao", "chi", "zhe",
                     "yi", "pen", "mao", "liang")
focal_region <- c(0.5, 3.5)  # mei-zhi-mao (first three syllables)

# Define syllable boundaries for precise marking
syllable_boundaries <- seq(0.5, 9.5, 1)
syllable_centers <- seq(1, 9, 1)

# Calculate pitch statistics for academic reporting
pitch_stats <- intonation %>%
  summarise(
    mean_f0 = mean(f00, na.rm = TRUE),
    sd_f0 = sd(f00, na.rm = TRUE),
    min_f0 = min(f00, na.rm = TRUE),
    max_f0 = max(f00, na.rm = TRUE),
    range_f0 = max_f0 - min_f0
  )

# Calculate focal region statistics
focal_data <- intonation %>%
  filter(point >= focal_region[1] & point <= focal_region[2])

non_focal_data <- intonation %>%
  filter(point < focal_region[1] | point > focal_region[2])

focal_stats <- focal_data %>%
  summarise(
    focal_mean = mean(f00, na.rm = TRUE),
    focal_max = max(f00, na.rm = TRUE)
  )

non_focal_stats <- non_focal_data %>%
  summarise(
    non_focal_mean = mean(f00, na.rm = TRUE)
  )

cat("\nPitch statistics (Hz):\n")
print(pitch_stats)
cat("\nFocal vs Non-focal comparison:\n")
cat(sprintf("Focal region mean: %.1f Hz\n", focal_stats$focal_mean))
cat(sprintf("Non-focal region mean: %.1f Hz\n", non_focal_stats$non_focal_mean))
cat(sprintf("Focal prominence: +%.1f Hz\n",
            focal_stats$focal_mean - non_focal_stats$non_focal_mean))

# Create professional prosody plot
p <- ggplot(data = intonation, aes(x = point, y = f00)) +

  # Add focal region highlighting (subtle background)
  annotate("rect",
           xmin = focal_region[1], xmax = focal_region[2],
           ymin = -Inf, ymax = Inf,
           fill = "#E8F4FD", alpha = 0.3) +

  # Main pitch contour line
  geom_line(linewidth = 1.5, color = "#2E86AB", alpha = 0.9) +

  # Add data points for clarity
  geom_point(size = 2.5, color = "#2E86AB", alpha = 0.8) +

  # Highlight focal region with different color/style
  geom_line(data = focal_data,
            linewidth = 2.0, color = "#A23B72") +
  geom_point(data = focal_data,
             size = 3.0, color = "#A23B72") +

  # Professional theme
  theme_classic(base_size = 12) +
  theme(
    # Panel and background
    panel.background = element_rect(fill = "white", color = NA),
    plot.background = element_rect(fill = "white", color = NA),

    # Grid lines (minimal, academic style)
    panel.grid.major.y = element_line(color = "grey90", linewidth = 0.5, linetype = "dotted"),
    panel.grid.minor = element_blank(),
    panel.grid.major.x = element_blank(),

    # Axes
    axis.line = element_line(color = "black", linewidth = 0.8),
    axis.ticks = element_line(color = "black", linewidth = 0.6),
    axis.ticks.length = unit(0.15, "cm"),

    # Text formatting
    axis.text.x = element_text(size = 11, color = "black", margin = margin(t = 5)),
    axis.text.y = element_text(size = 11, color = "black", margin = margin(r = 5)),
    axis.title.x = element_text(size = 13, color = "black", margin = margin(t = 10)),
    axis.title.y = element_text(size = 13, color = "black", margin = margin(r = 10)),

    # Plot margins
    plot.margin = margin(15, 15, 15, 15),

    # Title formatting
    plot.title = element_text(size = 14, face = "bold", hjust = 0.5, margin = margin(b = 15)),
    plot.subtitle = element_text(size = 11, hjust = 0.5, margin = margin(b = 10))
  ) +

  # Axis labels (academic style)
  labs(
    x = "Syllable Position",
    y = "Fundamental Frequency (Hz)",
    title = "Pitch Contour of Chinese Phrase: mei-zhi mao chi-zhe yi pen mao liang",
    subtitle = "Focal prominence on mei-zhi-mao (syllables 1-3)"
  ) +

  # X-axis configuration
  scale_x_continuous(
    limits = c(0.2, 9.8),
    breaks = seq(1, 9, 1),
    labels = syllable_labels,
    expand = c(0.02, 0)
  ) +

  # Y-axis configuration (academic formatting)
  scale_y_continuous(
    labels = function(x) paste0(x, " Hz"),
    expand = expansion(mult = c(0.05, 0.05))
  ) +

  # Add focal region annotation
  annotate("text",
           x = mean(focal_region),
           y = max(intonation$f00, na.rm = TRUE) * 1.05,
           label = "Focal Region\n(Pitch Accent)",
           size = 3.5,
           color = "#A23B72",
           fontface = "bold",
           hjust = 0.5) +

  # Add syllable boundary markers (subtle)
  geom_vline(xintercept = syllable_boundaries,
             color = "grey80",
             linewidth = 0.3,
             alpha = 0.7)

# Display the plot
print(p)

# Create alternative plot with pitch range normalization for comparison
p_normalized <- ggplot(data = intonation, aes(x = point, y = f00)) +

  # Add focal region highlighting
  annotate("rect",
           xmin = focal_region[1], xmax = focal_region[2],
           ymin = -Inf, ymax = Inf,
           fill = "#E8F4FD", alpha = 0.3) +

  # Main contour with smoothed line
  geom_smooth(method = "loess", span = 0.3, se = FALSE,
              linewidth = 1.5, color = "#2E86AB") +
  geom_point(size = 2, color = "#2E86AB", alpha = 0.7) +

  # Highlight focal region
  geom_smooth(data = focal_data, method = "loess", span = 0.3, se = FALSE,
              linewidth = 2.0, color = "#A23B72") +
  geom_point(data = focal_data, size = 3, color = "#A23B72") +

  # Professional theme (same as above)
  theme_classic(base_size = 12) +
  theme(
    panel.background = element_rect(fill = "white", color = NA),
    plot.background = element_rect(fill = "white", color = NA),
    panel.grid.major.y = element_line(color = "grey90",
                                      linewidth = 0.5,
                                      linetype = "dotted"),
    panel.grid.minor = element_blank(),
    panel.grid.major.x = element_blank(),
    axis.line = element_line(color = "black", linewidth = 0.8),
    axis.ticks = element_line(color = "black", linewidth = 0.6),
    axis.ticks.length = unit(0.15, "cm"),
    axis.text.x = element_text(size = 11, color = "black",
                               margin = margin(t = 5)),
    axis.text.y = element_text(size = 11, color = "black",
                               margin = margin(r = 5)),
    axis.title.x = element_text(size = 13, color = "black",
                                margin = margin(t = 10)),
    axis.title.y = element_text(size = 13, color = "black",
                                margin = margin(r = 10)),
    plot.margin = margin(15, 15, 15, 15),
    plot.title = element_text(size = 14, face = "bold", hjust = 0.5,
                              margin = margin(b = 15)),
    plot.subtitle = element_text(size = 11, hjust = 0.5,
                                 margin = margin(b = 10))
  ) +

  labs(
    x = "Syllable Position",
    y = "Fundamental Frequency (Hz)",
    title = "Smoothed Pitch Contour: mei-zhi mao chi-zhe yi pen mao liang",
    subtitle = "LOESS smoothing with focal prominence highlighted"
  ) +

  scale_x_continuous(
    limits = c(0.2, 9.8),
    breaks = syllable_centers,
    labels = syllable_labels,
    expand = c(0.02, 0)
  ) +

  scale_y_continuous(
    labels = function(x) paste0(x, " Hz"),
    expand = expansion(mult = c(0.05, 0.05))
  ) +

  annotate("text",
           x = mean(focal_region),
           y = max(intonation$f00, na.rm = TRUE) * 1.05,
           label = "Focal Region",
           size = 3.5,
           color = "#A23B72",
           fontface = "bold",
           hjust = 0.5) +

  geom_vline(xintercept = syllable_boundaries,
             color = "grey80",
             linewidth = 0.3,
             alpha = 0.7)

# Save both versions
ggsave(
  filename = "prosody_contour_academic.png",
  plot = p,
  width = 12,
  height = 7,
  units = "in",
  dpi = 300,
  bg = "white"
)

ggsave(
  filename = "prosody_contour_smoothed.png",
  plot = p_normalized,
  width = 12,
  height = 7,
  units = "in",
  dpi = 300,
  bg = "white"
)

# Save PDFs for publication
ggsave(
  filename = "prosody_contour_academic.pdf",
  plot = p,
  width = 12,
  height = 7,
  units = "in",
  dpi = 300,
  bg = "white"
)

ggsave(
  filename = "prosody_contour_smoothed.pdf",
  plot = p_normalized,
  width = 12,
  height = 7,
  units = "in",
  dpi = 300,
  bg = "white"
)

cat("\nPlots saved in multiple formats for publication:\n")
cat("- Raw data plot: prosody_contour_academic.png/.pdf\n")
cat("- Smoothed plot: prosody_contour_smoothed.png/.pdf\n")

# Print comprehensive statistics for academic reporting
cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("PROSODIC ANALYSIS SUMMARY\n")
cat(paste(rep("=", 60), collapse = ""), "\n")
cat(sprintf("Phrase: mei-zhi mao chi-zhe yi pen mao liang\n"))
cat(sprintf("Total syllables: %d\n", length(syllable_labels)))
cat(sprintf("Focal region: syllables 1-3 (%s)\n",
            paste(syllable_labels[1:3], collapse = "-")))
cat("\nPITCH STATISTICS:\n")
cat(sprintf("Overall mean F0: %.1f Hz (SD = %.1f Hz)\n",
            pitch_stats$mean_f0, pitch_stats$sd_f0))
cat(sprintf("F0 range: %.1f - %.1f Hz (%.1f Hz range)\n",
            pitch_stats$min_f0, pitch_stats$max_f0, pitch_stats$range_f0))
cat(sprintf("Focal region mean: %.1f Hz\n", focal_stats$focal_mean))
cat(sprintf("Non-focal region mean: %.1f Hz\n",
            non_focal_stats$non_focal_mean))
cat(sprintf("Focal prominence: +%.1f Hz (%.1f%% increase)\n",
            focal_stats$focal_mean - non_focal_stats$non_focal_mean,
            ((focal_stats$focal_mean / non_focal_stats$non_focal_mean) - 1) * 100))
cat(paste(rep("=", 60), collapse = ""), "\n")