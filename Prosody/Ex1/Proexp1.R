library(readxl)
library(ggplot2)
library(lme4)
library(lattice)
library(emmeans)
library(cowplot)
library(ggpubr)
library(lmerTest)
setwd("~/Downloads/scope/prosody/Exp1/Ex11")
intonation <-read_excel('normf1.xlsx')
intonation$point=as.numeric(intonation$point)

dsF<-ggplot(data=intonation, aes(x=point,y=f00))
dsF<-dsF+geom_line(linewidth=1,color = "blue")+theme_classic(base_size=18)
dsF

# modify the figure


ds<-dsF+scale_linetype_manual(values=c("solid"))+
  scale_size_manual(values=c(1,1.2))
ds

ds1<-ds+xlab("Syllable")+ylab("pitch (Hz)")
ds1
ds2 <- ds1 +
  scale_x_continuous(limits = c(0, 9), breaks = seq(0, 9, 1),labels = rep("", 10)) +
  xlab("") +
  ylab("Pitch (Hz)")
ds2

# 添加自定义文字标签在两个刻度之间
ds3 <- ds2 +
  annotate("text", x = 0.5, y = 160, label = "mei", vjust = 0, size = 5) +  
  annotate("text", x = 1.5, y = 160, label = "zhi", vjust = 0, size = 5) + 
  annotate("text", x = 2.5, y = 160, label = "mao", vjust = 0, size = 5) +
  annotate("text", x = 3.5, y = 160, label = "chi", vjust = 0, size = 5) +
  annotate("text", x = 4.5, y = 160, label = "zhe", vjust = 0, size = 5) +
  annotate("text", x = 5.5, y = 160, label = "yi", vjust = 0, size = 5) +
  annotate("text", x = 6.5, y = 160, label = "pen", vjust = 0, size = 5) +
  annotate("text", x = 7.5, y = 160, label = "mao", vjust = 0, size = 5) +
  annotate("text", x = 8.5, y = 160, label = "liang", vjust = 0, size = 5)
ds3
#ds3<-ds2+geom_vline(aes(xintercept=0),color='blue',linetype=2,linewidth=1.1)+geom_vline(aes(xintercept=2),color='blue',linetype=2,linewidth=1.1)+geom_vline(aes(xintercept=0),color='red',linetype=1)+geom_vline(aes(xintercept=2),color='red',linetype=1)
#ds4<-ds2+annotate("text", x=1, y=150, label="pitch accent",size=4,color='blue')+annotate("text", x=1, y=130, label="focal element",size=4,color='red')
ds5 <- ds3+  theme_bw()
ds5
ggsave(
  filename = "ProsodyExp1.png", # 保存的文件名称。通过后缀来决定生成什么格式的图片
  width = 7,             # 宽
  height = 4,            # 高
  units = "in",          # 单位
  dpi = 2000              # 分辨率DPI
)

